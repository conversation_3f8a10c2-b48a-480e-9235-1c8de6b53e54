# RailOps Firebase Cloud Functions

Firebase Cloud Functions for train location-based push notifications in the RailOps application.

## Overview

This directory contains the backend Firebase Cloud Functions that handle:
- Fetching train location data from `/microservice/train/location/` API
- Building coach tables with passenger counts (onboarding/off-boarding/vacant)
- Sending FCM notifications with custom sound "railops_alarm"
- Anti-duplication using Firestore `sentAlerts` collection
- Token lookup from Firestore `tokens/{user_id}` collection

## Setup

### Prerequisites
- Node.js 18+
- Firebase CLI installed globally: `npm install -g firebase-tools`
- Firebase project: `railwaysapp-prod` (ID: 513557807469)

### Installation
```bash
cd functions
npm install
```

### Configuration

1. **Set RAILOPS_BEARER secret:**
```bash
firebase functions:secrets:set RAILOPS_BEARER
# Enter your API bearer token when prompted
```

2. **Login to Firebase:**
```bash
firebase login
```

3. **Set Firebase project:**
```bash
firebase use railwaysapp-prod
```

## Development

### Build and Test
```bash
# Build TypeScript
npm run build

# Run linting
npm run lint

# Fix linting issues
npm run lint:fix

# Run tests
npm run test

# Run tests with coverage
npm run test:coverage
```

### Local Development
```bash
# Start Firebase emulators
npm run serve

# The function will be available at:
# http://localhost:5001/railwaysapp-prod/us-central1/notify
```

## Deployment

### Deploy Functions Only
```bash
npm run deploy
```

### Deploy All Firebase Resources
```bash
firebase deploy
```

## API Usage

### POST /notify Endpoint

**URL:** `https://us-central1-railwaysapp-prod.cloudfunctions.net/notify`

**Method:** POST

**Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "user_id": "user123",
  "train_number": "12345",
  "date": "2024-12-20",
  "lat": "28.6139",
  "lng": "77.2090"
}
```

**Response (Success):**
```json
{
  "message": "Notification sent successfully",
  "user_id": "user123",
  "train_number": "12345",
  "stations_count": 3,
  "timestamp": "2024-12-20T10:30:00.000Z"
}
```

**Response (Already Sent):**
```json
{
  "message": "Alert already sent",
  "skipped": true
}
```

### cURL Example
```bash
curl -X POST https://us-central1-railwaysapp-prod.cloudfunctions.net/notify \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "train_number": "12345", 
    "date": "2024-12-20",
    "lat": "28.6139",
    "lng": "77.2090"
  }'
```

## Firestore Collections

### tokens/{user_id}
Stores FCM tokens for users:
```json
{
  "fcm_token": "dGhpcyBpcyBhIGZha2UgdG9rZW4...",
  "device_info": {...},
  "updated_at": "2024-12-20T10:30:00.000Z"
}
```

### sentAlerts/{alertKey}
Prevents duplicate notifications:
```json
{
  "timestamp": "2024-12-20T10:30:00.000Z",
  "sent_at": "2024-12-20T10:30:00.000Z"
}
```

Alert key format: `{user_id}/{date}/{train_number}`

## Notification Format

**Title:** `Train {train_number} - Coach Updates`

**Body:**
```
Passenger counts updated for your assigned coaches:

Station | Coach | Onboarding/Off-boarding/Vacant
──────────────────────────────────────────────────
DEL | A1 | 15/8/12
DEL | B2 | 20/5/10
GZB | A1 | 18/10/7
```

**Custom Sound:** `railops_alarm` (Android) / `railops_alarm.caf` (iOS)

**Notification Channel:** `railops_alerts`

## Error Handling

The function handles various error scenarios:
- Missing required parameters (400)
- User FCM token not found (404) 
- API authentication failures (500)
- Train location data fetch failures (500)
- FCM notification send failures (500)

## Monitoring

View function logs:
```bash
firebase functions:log
```

Monitor in Firebase Console:
- Functions → railwaysapp-prod → notify
- Firestore → sentAlerts collection
- Firestore → tokens collection

## Security

- RAILOPS_BEARER token stored as Firebase secret
- Firestore rules restrict access to user's own tokens
- sentAlerts collection only accessible by Cloud Functions
- CORS enabled for web client access
