#!/usr/bin/env node

/**
 * Test script to validate Firebase Functions setup
 * Run with: node test-setup.js
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing RailOps Firebase Functions Setup...\n');

// Test 1: Check required files exist
const requiredFiles = [
  'package.json',
  'tsconfig.json',
  '.eslintrc.js',
  'src/index.ts',
  'src/index.test.ts',
  'README.md'
];

console.log('📁 Checking required files...');
let filesOk = true;
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file} - MISSING`);
    filesOk = false;
  }
});

// Test 2: Check package.json dependencies
console.log('\n📦 Checking package.json dependencies...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredDeps = [
    'firebase-admin',
    'firebase-functions',
    'axios'
  ];
  
  let depsOk = true;
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`  ✅ ${dep}: ${packageJson.dependencies[dep]}`);
    } else {
      console.log(`  ❌ ${dep} - MISSING`);
      depsOk = false;
    }
  });
  
  // Check Node.js version
  if (packageJson.engines && packageJson.engines.node === '18') {
    console.log(`  ✅ Node.js engine: ${packageJson.engines.node}`);
  } else {
    console.log(`  ⚠️  Node.js engine should be 18`);
  }
  
} catch (error) {
  console.log('  ❌ Error reading package.json:', error.message);
  filesOk = false;
}

// Test 3: Check TypeScript configuration
console.log('\n🔧 Checking TypeScript configuration...');
try {
  const tsConfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'));
  if (tsConfig.compilerOptions && tsConfig.compilerOptions.target === 'es2017') {
    console.log('  ✅ TypeScript target: es2017');
  } else {
    console.log('  ⚠️  TypeScript target should be es2017');
  }
  
  if (tsConfig.compilerOptions && tsConfig.compilerOptions.outDir === 'lib') {
    console.log('  ✅ Output directory: lib');
  } else {
    console.log('  ⚠️  Output directory should be lib');
  }
} catch (error) {
  console.log('  ❌ Error reading tsconfig.json:', error.message);
}

// Test 4: Check main function export
console.log('\n⚡ Checking main function export...');
try {
  const indexTs = fs.readFileSync('src/index.ts', 'utf8');
  if (indexTs.includes('export const notify')) {
    console.log('  ✅ notify function exported');
  } else {
    console.log('  ❌ notify function not found');
    filesOk = false;
  }
  
  if (indexTs.includes('functions.https.onRequest')) {
    console.log('  ✅ HTTP request handler configured');
  } else {
    console.log('  ❌ HTTP request handler not found');
    filesOk = false;
  }
  
  if (indexTs.includes('admin.initializeApp')) {
    console.log('  ✅ Firebase Admin initialized');
  } else {
    console.log('  ❌ Firebase Admin initialization not found');
    filesOk = false;
  }
} catch (error) {
  console.log('  ❌ Error reading src/index.ts:', error.message);
  filesOk = false;
}

// Test 5: Check Firebase configuration
console.log('\n🔥 Checking Firebase configuration...');
const firebaseConfigPath = '../firebase.json';
if (fs.existsSync(firebaseConfigPath)) {
  try {
    const firebaseConfig = JSON.parse(fs.readFileSync(firebaseConfigPath, 'utf8'));
    if (firebaseConfig.functions && firebaseConfig.functions.source === 'functions') {
      console.log('  ✅ Functions source configured');
    } else {
      console.log('  ❌ Functions source not configured correctly');
    }
    
    if (firebaseConfig.functions && firebaseConfig.functions.runtime === 'nodejs18') {
      console.log('  ✅ Node.js 18 runtime configured');
    } else {
      console.log('  ❌ Node.js 18 runtime not configured');
    }
  } catch (error) {
    console.log('  ❌ Error reading firebase.json:', error.message);
  }
} else {
  console.log('  ❌ firebase.json not found in parent directory');
  filesOk = false;
}

// Summary
console.log('\n📊 Setup Validation Summary:');
if (filesOk) {
  console.log('✅ All checks passed! Your Firebase Functions setup is ready.');
  console.log('\n🚀 Next steps:');
  console.log('1. Install dependencies: npm install');
  console.log('2. Set RAILOPS_BEARER secret: firebase functions:secrets:set RAILOPS_BEARER');
  console.log('3. Build and deploy: npm run deploy');
  console.log('4. Test the endpoint with the provided curl command');
} else {
  console.log('❌ Some issues found. Please fix them before deploying.');
  process.exit(1);
}

console.log('\n🔗 Documentation: See functions/README.md for detailed instructions');
