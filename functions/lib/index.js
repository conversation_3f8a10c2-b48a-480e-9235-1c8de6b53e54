"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.notify = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
const axios_1 = __importDefault(require("axios"));
// Initialize Firebase Admin SDK
admin.initializeApp();
/**
 * Get FCM token for a specific user from Firestore
 */
const getUserFcmToken = async (userId) => {
    try {
        const doc = await admin.firestore().collection("tokens").doc(userId).get();
        const data = doc.data();
        return (data === null || data === void 0 ? void 0 : data.fcm_token) || null;
    }
    catch (error) {
        console.error(`Error fetching FCM token for user ${userId}:`, error);
        return null;
    }
};
/**
 * Check if alert has already been sent to prevent duplicates
 */
const isAlertAlreadySent = async (alertKey) => {
    try {
        const doc = await admin.firestore().collection("sentAlerts").doc(alertKey).get();
        return doc.exists;
    }
    catch (error) {
        console.error(`Error checking sent alerts for ${alertKey}:`, error);
        return false;
    }
};
/**
 * Mark alert as sent in Firestore
 */
const markAlertAsSent = async (alertKey) => {
    try {
        await admin.firestore().collection("sentAlerts").doc(alertKey).set({
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            sent_at: new Date().toISOString(),
        });
    }
    catch (error) {
        console.error(`Error marking alert as sent for ${alertKey}:`, error);
    }
};
/**
 * Fetch train location data from microservice
 */
const fetchTrainLocationData = async (trainNumber, date, bearerToken) => {
    try {
        const response = await axios_1.default.get("https://railopsapi.biputri.com/microservice/train/location/", {
            params: {
                train_number: trainNumber,
                date: date,
            },
            headers: {
                "Authorization": `Bearer ${bearerToken}`,
                "Content-Type": "application/json",
            },
            timeout: 10000, // 10 second timeout
        });
        return response.data;
    }
    catch (error) {
        console.error("Error fetching train location data:", error);
        return null;
    }
};
/**
 * Build coach table string for notification body
 */
const buildCoachTable = (stations) => {
    let table = "Station | Coach | Onboarding/Off-boarding/Vacant\n";
    table += "─".repeat(50) + "\n";
    stations.forEach((station) => {
        station.coaches.forEach((coach) => {
            const counts = `${coach.onboarding_count}/${coach.off_boarding_count}/${coach.vacant_count}`;
            table += `${station.station_code} | ${coach.coach_number} | ${counts}\n`;
        });
    });
    return table;
};
/**
 * Send FCM notification with custom sound
 */
const sendFcmNotification = async (fcmToken, payload) => {
    try {
        const message = {
            token: fcmToken,
            notification: {
                title: payload.title,
                body: payload.body,
            },
            data: payload.data,
            android: {
                notification: {
                    channelId: "railops_alerts",
                    sound: "railops_alarm",
                    priority: "high",
                },
            },
            apns: {
                payload: {
                    aps: {
                        sound: "railops_alarm.caf",
                        category: "RAILOPS_ALERT",
                    },
                },
            },
        };
        const response = await admin.messaging().send(message);
        console.log("FCM message sent successfully:", response);
        return true;
    }
    catch (error) {
        console.error("Error sending FCM message:", error);
        return false;
    }
};
/**
 * Main Cloud Function: POST /notify endpoint
 * Fetches train location data, builds coach tables, and sends FCM notifications
 */
exports.notify = functions.https.onRequest(async (req, res) => {
    var _a, _b;
    // Enable CORS
    res.set("Access-Control-Allow-Origin", "*");
    res.set("Access-Control-Allow-Methods", "POST, OPTIONS");
    res.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
    if (req.method === "OPTIONS") {
        res.status(200).send();
        return;
    }
    if (req.method !== "POST") {
        res.status(405).json({ error: "Method not allowed. Use POST." });
        return;
    }
    try {
        // Extract request parameters
        const { user_id, train_number, date, lat, lng } = req.body;
        // Validate required parameters
        if (!user_id || !train_number || !date) {
            res.status(400).json({
                error: "Missing required parameters: user_id, train_number, date",
            });
            return;
        }
        // Log coordinates if provided (for future proximity-based features)
        if (lat && lng) {
            console.log(`Request coordinates: lat=${lat}, lng=${lng}`);
        }
        console.log(`Processing notification request for user: ${user_id}, train: ${train_number}, date: ${date}`);
        // Get RAILOPS_BEARER secret
        const bearerToken = (_a = functions.config().railops) === null || _a === void 0 ? void 0 : _a.bearer;
        if (!bearerToken) {
            console.error("RAILOPS_BEARER secret not configured");
            res.status(500).json({ error: "Server configuration error" });
            return;
        }
        // Check for duplicate alerts
        const alertKey = `${user_id}/${date}/${train_number}`;
        const alreadySent = await isAlertAlreadySent(alertKey);
        if (alreadySent) {
            console.log(`Alert already sent for ${alertKey}, skipping`);
            res.status(200).json({ message: "Alert already sent", skipped: true });
            return;
        }
        // Get user's FCM token
        const fcmToken = await getUserFcmToken(user_id);
        if (!fcmToken) {
            console.error(`No FCM token found for user: ${user_id}`);
            res.status(404).json({ error: "User FCM token not found" });
            return;
        }
        // Fetch train location data
        const trainData = await fetchTrainLocationData(train_number, date, bearerToken);
        if (!trainData) {
            console.error(`Failed to fetch train location data for ${train_number}`);
            res.status(500).json({ error: "Failed to fetch train location data" });
            return;
        }
        // Build coach table for notification
        const coachTable = buildCoachTable(trainData.stations);
        // Create notification payload
        const notificationPayload = {
            title: `Train ${train_number} - Coach Updates`,
            body: `Passenger counts updated for your assigned coaches:\n\n${coachTable}`,
            data: {
                type: "train_location_update",
                station_code: ((_b = trainData.stations[0]) === null || _b === void 0 ? void 0 : _b.station_code) || "",
                coach_data: JSON.stringify(trainData.stations),
                timestamp: new Date().toISOString(),
            },
        };
        // Send FCM notification
        const notificationSent = await sendFcmNotification(fcmToken, notificationPayload);
        if (!notificationSent) {
            res.status(500).json({ error: "Failed to send notification" });
            return;
        }
        // Mark alert as sent to prevent duplicates
        await markAlertAsSent(alertKey);
        console.log(`Notification sent successfully to user: ${user_id}`);
        res.status(200).json({
            message: "Notification sent successfully",
            user_id: user_id,
            train_number: train_number,
            stations_count: trainData.stations.length,
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        console.error("Error in notify function:", error);
        res.status(500).json({
            error: "Internal server error",
            details: error instanceof Error ? error.message : "Unknown error",
        });
    }
});
//# sourceMappingURL=index.js.map